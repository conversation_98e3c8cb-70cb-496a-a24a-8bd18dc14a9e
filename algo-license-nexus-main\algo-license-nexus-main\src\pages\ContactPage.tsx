import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Link } from "react-router-dom";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageSquare,
  Calendar,
  Users,
  Building,
  Globe,
  Shield
} from "lucide-react";

/**
 * Contact page component providing multiple contact methods and inquiry forms
 * Features contact form, office locations, and support information
 * Includes specialized contact options for different client types
 */
const ContactPage = () => {
  const contactMethods = [
    {
      icon: Phone,
      title: "Phone",
      primary: "+****************",
      secondary: "Mon-Fri 9AM-6PM EST",
      description: "Direct line to our sales team"
    },
    {
      icon: Mail,
      title: "Email",
      primary: "<EMAIL>",
      secondary: "Response within 24 hours",
      description: "General inquiries and support"
    },
    {
      icon: MessageSquare,
      title: "Live Chat",
      primary: "Available Now",
      secondary: "Mon-Fri 9AM-9PM EST",
      description: "Instant support for existing clients"
    },
    {
      icon: Calendar,
      title: "Schedule Meeting",
      primary: "Book Consultation",
      secondary: "30-60 minute sessions",
      description: "Personalized strategy discussion"
    }
  ];

  const offices = [
    {
      city: "Remote Operations",
      address: "Digital-First Business",
      zipcode: "Serving Clients Globally",
      phone: "+****************",
      type: "Headquarters"
    }
  ];

  const supportTypes = [
    {
      icon: Building,
      title: "Institutional Sales",
      description: "For hedge funds, asset managers, and institutional investors",
      contact: "<EMAIL>"
    },
    {
      icon: Users,
      title: "Technical Support",
      description: "Implementation assistance and technical questions",
      contact: "<EMAIL>"
    },
    {
      icon: Shield,
      title: "Compliance & Legal",
      description: "Regulatory questions and compliance support",
      contact: "<EMAIL>"
    },
    {
      icon: Globe,
      title: "Partnership Inquiries",
      description: "Strategic partnerships and integration opportunities",
      contact: "<EMAIL>"
    }
  ];

  return (
    <Layout>
      <PageHeader
        title="Contact Our Team"
        subtitle="Get In Touch"
        description="Ready to transform your trading capabilities? Our team of experts is here to help you find the perfect AI strategy solution for your organization."
        icon={MessageSquare}
      />

      {/* Contact Methods */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactMethods.map((method, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 text-center">
                <CardContent className="p-8">
                  <method.icon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">{method.title}</h3>
                  <p className="text-blue-400 font-medium mb-1">{method.primary}</p>
                  <p className="text-gray-400 text-sm mb-3">{method.secondary}</p>
                  <p className="text-gray-500 text-xs">{method.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form and Office Info */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-white mb-8">
                Send Us a <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Message</span>
              </h2>

              <Card className="bg-white/5 backdrop-blur-sm border-white/10">
                <CardContent className="p-8">
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="firstName" className="text-white">First Name</Label>
                        <Input
                          id="firstName"
                          placeholder="John"
                          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName" className="text-white">Last Name</Label>
                        <Input
                          id="lastName"
                          placeholder="Doe"
                          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-white">Company</Label>
                      <Input
                        id="company"
                        placeholder="Your Company Name"
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="inquiryType" className="text-white">Inquiry Type</Label>
                      <Select>
                        <SelectTrigger className="bg-white/10 border-white/20 text-white">
                          <SelectValue placeholder="Select inquiry type" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-white/20">
                          <SelectItem value="strategy-licensing">Strategy Licensing</SelectItem>
                          <SelectItem value="technical-support">Technical Support</SelectItem>
                          <SelectItem value="partnership">Partnership Opportunity</SelectItem>
                          <SelectItem value="compliance">Compliance Question</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-white">Message</Label>
                      <Textarea
                        id="message"
                        placeholder="Tell us about your requirements and how we can help..."
                        rows={6}
                        className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                      />
                    </div>

                    <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg">
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Office Locations */}
            <div>
              <h2 className="text-3xl font-bold text-white mb-8">
                Global <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Offices</span>
              </h2>

              <div className="space-y-6">
                {offices.map((office, index) => (
                  <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-xl text-white">{office.city}</CardTitle>
                        <span className="text-sm text-blue-400 bg-blue-400/10 px-3 py-1 rounded-full">
                          {office.type}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <MapPin className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-gray-300">{office.address}</p>
                          <p className="text-gray-400 text-sm">{office.zipcode}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Phone className="w-5 h-5 text-gray-400 flex-shrink-0" />
                        <p className="text-gray-300">{office.phone}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Specialized Support */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Specialized <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Support</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Connect with the right team for your specific needs and requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {supportTypes.map((support, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
                <CardContent className="p-8">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <support.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">{support.title}</h3>
                      <p className="text-gray-400 mb-4">{support.description}</p>
                      <a
                        href={`mailto:${support.contact}`}
                        className="text-blue-400 hover:text-blue-300 transition-colors font-medium"
                      >
                        {support.contact}
                      </a>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Business Hours */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Business <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Hours</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Our global team ensures support coverage across all major time zones.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white/5 backdrop-blur-sm border-white/10">
              <CardContent className="p-8 text-center">
                <Clock className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Americas</h3>
                <p className="text-gray-400">Monday - Friday</p>
                <p className="text-gray-400">9:00 AM - 6:00 PM EST</p>
              </CardContent>
            </Card>

            <Card className="bg-white/5 backdrop-blur-sm border-white/10">
              <CardContent className="p-8 text-center">
                <Clock className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Europe</h3>
                <p className="text-gray-400">Monday - Friday</p>
                <p className="text-gray-400">9:00 AM - 6:00 PM GMT</p>
              </CardContent>
            </Card>

            <Card className="bg-white/5 backdrop-blur-sm border-white/10">
              <CardContent className="p-8 text-center">
                <Clock className="w-12 h-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Asia-Pacific</h3>
                <p className="text-gray-400">Monday - Friday</p>
                <p className="text-gray-400">9:00 AM - 6:00 PM SGT</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 p-8 bg-blue-500/10 border border-blue-500/20 rounded-2xl">
            <h3 className="text-2xl font-bold text-white mb-4">Emergency Support</h3>
            <p className="text-gray-300 mb-6">
              Sovereign tier clients have access to 24/7 emergency support for critical issues.
            </p>
            <Link
              to="/pricing"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Learn About Sovereign Support
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ContactPage;
