import { Layout } from "@/components/Layout";
import { <PERSON><PERSON>eader } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  Target,
  Database,
  Cpu,
  BarChart3,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Star
} from "lucide-react";

/**
 * Services page showcasing detailed AI strategy offerings
 * Features comprehensive strategy descriptions, performance metrics, and implementation details
 * Organized by strategy tiers with detailed technical specifications
 */
const Services = () => {
  const strategies = [
    {
      id: "quantum-trading",
      icon: TrendingUp,
      title: "Quantum Trading Algorithms",
      tier: "Tier 1",
      description: "Revolutionary quantum-inspired trading strategies with 45% average ROI",
      longDescription: "Our flagship quantum trading algorithms leverage quantum computing principles to analyze market patterns at unprecedented speeds. These strategies utilize quantum superposition concepts to evaluate multiple market scenarios simultaneously, providing superior risk-adjusted returns.",
      features: [
        "Real-time market analysis with quantum-inspired processing",
        "Advanced risk optimization using quantum annealing techniques",
        "Predictive modeling with 95% accuracy rate",
        "Multi-asset correlation analysis",
        "Adaptive position sizing algorithms"
      ],
      performance: {
        roi: "45%",
        sharpe: "1.8",
        maxDrawdown: "12.2%",
        winRate: "63%"
      },
      color: "from-blue-500 to-cyan-500",
      price: "$15,000"
    },
    {
      id: "neural-prediction",
      icon: Target,
      title: "Neural Prediction Engine",
      tier: "Tier 1",
      description: "Advanced neural networks for market forecasting and trend prediction",
      longDescription: "Deep learning models trained on decades of market data, news sentiment, and macroeconomic indicators. Our neural prediction engine identifies market trends before they become apparent to traditional analysis methods.",
      features: [
        "Deep learning models with 10+ hidden layers",
        "Real-time pattern recognition across 50+ indicators",
        "Sentiment analysis from 100+ news sources daily",
        "Macroeconomic correlation modeling",
        "Adaptive learning from market feedback"
      ],
      performance: {
        roi: "38%",
        sharpe: "1.6",
        maxDrawdown: "15.1%",
        winRate: "58%"
      },
      color: "from-purple-500 to-pink-500",
      price: "$12,000"
    },
    {
      id: "portfolio-optimizer",
      icon: Database,
      title: "Adaptive Portfolio Optimizer",
      tier: "Tier 2",
      description: "Self-learning portfolio management with dynamic risk adjustment",
      longDescription: "Intelligent portfolio optimization that continuously adapts to changing market conditions. Uses reinforcement learning to optimize asset allocation and risk management in real-time.",
      features: [
        "Dynamic portfolio rebalancing every 15 minutes",
        "Risk assessment using 100+ risk factors",
        "Performance tracking with real-time attribution",
        "Stress testing against historical scenarios",
        "ESG integration capabilities"
      ],
      performance: {
        roi: "195%",
        sharpe: "2.2",
        maxDrawdown: "15.3%",
        winRate: "64%"
      },
      color: "from-green-500 to-emerald-500",
      price: "$50,000"
    },
    {
      id: "multi-asset",
      icon: Cpu,
      title: "Multi-Asset Strategy Suite",
      tier: "Tier 2",
      description: "Comprehensive algorithmic strategies for diverse asset classes",
      longDescription: "A complete suite of strategies covering equities, fixed income, commodities, currencies, and cryptocurrencies. Designed for institutions requiring broad market exposure with consistent alpha generation.",
      features: [
        "Cross-market arbitrage detection",
        "Liquidity optimization across venues",
        "Currency hedging automation",
        "Commodity seasonality modeling",
        "Cryptocurrency momentum strategies"
      ],
      performance: {
        roi: "220%",
        sharpe: "2.0",
        maxDrawdown: "18.7%",
        winRate: "61%"
      },
      color: "from-orange-500 to-red-500",
      price: "$50,000"
    }
  ];

  const supportServices = [
    {
      icon: Shield,
      title: "Implementation Support",
      description: "Dedicated team to ensure seamless integration of strategies into your existing infrastructure."
    },
    {
      icon: BarChart3,
      title: "Performance Monitoring",
      description: "Real-time performance tracking with detailed analytics and attribution reporting."
    },
    {
      icon: Zap,
      title: "Strategy Optimization",
      description: "Continuous refinement and parameter tuning based on market conditions and performance feedback."
    }
  ];

  return (
    <Layout>
      <PageHeader
        title="AI Strategy Portfolio"
        subtitle="Premium Algorithms"
        description="Explore our exclusive collection of AI-driven trading strategies, each designed to deliver superior risk-adjusted returns for elite financial institutions."
        icon={TrendingUp}
      />

      {/* Strategy Overview */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-white/5 backdrop-blur-sm border border-white/10">
              <TabsTrigger value="all" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">All Strategies</TabsTrigger>
              <TabsTrigger value="tier1" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">Tier 1</TabsTrigger>
              <TabsTrigger value="tier2" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">Tier 2</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {strategies.map((strategy) => (
                  <StrategyCard key={strategy.id} strategy={strategy} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="tier1" className="mt-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {strategies.filter(s => s.tier === "Tier 1").map((strategy) => (
                  <StrategyCard key={strategy.id} strategy={strategy} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="tier2" className="mt-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {strategies.filter(s => s.tier === "Tier 2").map((strategy) => (
                  <StrategyCard key={strategy.id} strategy={strategy} />
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Support Services */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Support <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Services</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Comprehensive support to ensure your success with our AI strategies.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {supportServices.map((service, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                    <service.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl text-white">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-400">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
};

// Strategy Card Component
const StrategyCard = ({ strategy }: { strategy: any }) => (
  <Card className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 group">
    <CardHeader>
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-xl bg-gradient-to-r ${strategy.color} bg-opacity-20`}>
          <strategy.icon className="w-8 h-8 text-white" />
        </div>
        <Badge variant="secondary" className="bg-gradient-to-r from-gold-400 to-yellow-500 text-black font-semibold">
          {strategy.tier}
        </Badge>
      </div>
      <CardTitle className="text-2xl text-white group-hover:text-blue-400 transition-colors">
        {strategy.title}
      </CardTitle>
      <CardDescription className="text-gray-300 text-lg">
        {strategy.description}
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-6">
      <p className="text-gray-400 leading-relaxed">{strategy.longDescription}</p>

      {/* Performance Metrics */}
      <div className="grid grid-cols-2 gap-4 p-4 bg-white/5 rounded-lg">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{strategy.performance.roi}</div>
          <div className="text-xs text-gray-500">Average ROI</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{strategy.performance.sharpe}</div>
          <div className="text-xs text-gray-500">Sharpe Ratio</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-400">{strategy.performance.maxDrawdown}</div>
          <div className="text-xs text-gray-500">Max Drawdown</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-400">{strategy.performance.winRate}</div>
          <div className="text-xs text-gray-500">Win Rate</div>
        </div>
      </div>

      {/* Key Features */}
      <div className="space-y-2">
        <h4 className="text-white font-semibold mb-3">Key Features:</h4>
        {strategy.features.slice(0, 3).map((feature: string, idx: number) => (
          <div key={idx} className="flex items-center text-gray-400 text-sm">
            <CheckCircle className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
            {feature}
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between pt-4">
        <div className="text-white">
          <span className="text-2xl font-bold">{strategy.price}</span>
          <span className="text-gray-400 text-sm ml-2">per strategy</span>
        </div>
        <Link
          to="/contact"
          onClick={() => {
            // Always scroll to top when navigating to contact
            setTimeout(() => {
              window.scrollTo({ top: 0, behavior: 'instant' });
            }, 10);
          }}
        >
          <Button className={`bg-gradient-to-r ${strategy.color} hover:opacity-90 text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300`}>
            Learn More
            <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </Link>
      </div>
    </CardContent>
  </Card>
);

export default Services;
