import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollIndicator } from "@/components/ScrollIndicator";
import { ScrollReveal, StaggeredReveal, Parallax, CinematicReveal } from "@/components/ScrollReveal";
import { useCinematicScroll } from "@/hooks/useScrollAnimation";
import { ArrowRight, Brain, Shield, Zap, TrendingUp, Star, Sparkles } from "lucide-react";

/**
 * Modern Hero component with enhanced animations and interactions
 * Features dynamic background effects, improved typography, and better CTAs
 * Includes floating elements and modern glassmorphism design
 */
export const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Enhanced cinematic scroll effects for background elements
  const backgroundParallax = useCinematicScroll({
    parallaxSpeed: 0.3,
    enableScale: true,
    scaleRange: [1, 1.1],
    enableParallax: true
  });

  const floatingElementsParallax = useCinematicScroll({
    parallaxSpeed: 0.6,
    enableScale: true,
    scaleRange: [0.95, 1.05],
    enableRotation: true,
    rotationRange: [-1, 1]
  });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const benefits = [
    {
      icon: TrendingUp,
      title: "340% Average ROI",
      description: "Our clients consistently outperform the market by 3.4x with strategies that adapt in real-time",
      color: "text-green-400",
      gradient: "from-green-500/20 to-emerald-500/20",
      proof: "Verified by PwC"
    },
    {
      icon: Shield,
      title: "Exclusive Market Edge",
      description: "Join the elite 200 institutions with access to strategies your competitors can't get",
      color: "text-blue-400",
      gradient: "from-blue-500/20 to-cyan-500/20",
      proof: "Limited to 200 clients"
    },
    {
      icon: Zap,
      title: "Instant Implementation",
      description: "Deploy in 48 hours with our white-glove setup and see results within the first week",
      color: "text-purple-400",
      gradient: "from-purple-500/20 to-pink-500/20",
      proof: "48hr guarantee"
    }
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center px-6 overflow-hidden">
      {/* Refined Background Animation */}
      <div className="absolute inset-0">
        {/* Working Parallax Gradient Orbs */}
        <Parallax speed={0.3}>
          <div
            className="absolute w-[800px] h-[800px] bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-full mix-blend-multiply filter blur-3xl animate-float-slow opacity-60"
            style={{
              left: `${15 + mousePosition.x * 0.008}%`,
              top: `${5 + mousePosition.y * 0.008}%`,
            }}
          />
        </Parallax>
        <Parallax speed={0.5}>
          <div
            className="absolute w-[600px] h-[600px] bg-gradient-to-r from-purple-500/12 to-pink-500/12 rounded-full mix-blend-multiply filter blur-3xl animate-drift opacity-50"
            style={{
              right: `${10 + mousePosition.x * 0.006}%`,
              bottom: `${15 + mousePosition.y * 0.006}%`,
            }}
          />
        </Parallax>
        <Parallax speed={0.2}>
          <div
            className="absolute w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-breathe opacity-40"
            style={{
              left: `${50 + mousePosition.x * 0.004}%`,
              top: `${50 + mousePosition.y * 0.004}%`,
            }}
          />
        </Parallax>

        {/* Refined floating particles */}
        <div className="absolute inset-0">
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="absolute w-0.5 h-0.5 bg-white/15 rounded-full animate-pulse-subtle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 4}s`,
                animationDuration: `${6 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10">
        {/* Social Proof Badge with Cinematic Animation */}
        <CinematicReveal delay={100} animationType="sequential">
          <div className="flex justify-center mb-12">
          <div className="glass-strong px-8 py-4 rounded-full border border-green-500/30 bg-green-500/10">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <Shield className="w-4 h-4 text-green-400" />
              </div>
              <span className="text-sm font-bold text-white letter-spacing-wide">
                50+ GROWING BUSINESSES TRUST US
              </span>
              <div className="flex items-center space-x-1">
                <span className="text-green-400 font-bold text-sm">$1M+</span>
                <span className="text-xs text-gray-200">MANAGED</span>
              </div>
            </div>
          </div>
          </div>
        </CinematicReveal>

        {/* Problem-Focused Headline with Cinematic Animation */}
        <CinematicReveal delay={300} animationType="cinematic">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-10 leading-none letter-spacing-tight">
            <span className="block text-balance">Stop Losing to</span>
            <span className="block text-gradient-blue animate-text-shimmer bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent bg-size-200">AI-Powered</span>
            <span className="block text-balance">Competitors</span>
          </h1>
        </CinematicReveal>

        {/* Benefit-Driven Subheading */}
        <CinematicReveal delay={400} animationType="sequential">
          <p className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed font-light text-balance">
            While your competitors gain <span className="text-green-400 font-semibold">340% average returns</span> with our exclusive AI strategies,
            you're still using outdated methods. <span className="text-red-400 font-semibold">Don't get left behind.</span>
          </p>
        </CinematicReveal>

        {/* Urgency Message */}
        <CinematicReveal delay={500} animationType="sequential">
          <div className="mb-16">
            <div className="inline-flex items-center space-x-2 glass-strong border border-red-500/30 px-6 py-3 rounded-full bg-red-500/15">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span className="text-red-300 font-bold text-sm">
                Only 12 exclusive licenses remaining this quarter
              </span>
            </div>
          </div>
        </CinematicReveal>

        {/* Conversion-Focused CTA Buttons */}
        <CinematicReveal delay={600} animationType="cinematic">
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-20">
          <Link
            to="/pricing"
            onClick={() => {
              // Always scroll to top when navigating to pricing
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'instant' });
              }, 10);
            }}
          >
            <Button className="group bg-gradient-to-r from-green-600 via-emerald-600 to-green-600 text-white px-12 py-4 text-lg font-bold rounded-2xl transition-all duration-300 hover-lift shadow-glow-subtle hover:shadow-glow">
              <TrendingUp className="mr-3 w-5 h-5 group-hover:rotate-3 transition-transform duration-300" />
              Secure Your License Now
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-0.5 transition-transform duration-300" />
            </Button>
          </Link>
          <Link
            to="/contact"
            onClick={() => {
              // Always scroll to top when navigating to contact
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'instant' });
              }, 10);
            }}
          >
            <Button className="group glass-strong text-white hover:bg-white/10 border border-white/30 hover:border-white/40 px-12 py-4 text-lg font-semibold rounded-2xl transition-all duration-300 hover-lift">
              Get Free ROI Analysis
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-0.5 transition-transform duration-300" />
            </Button>
          </Link>
          </div>
        </CinematicReveal>

        {/* Risk Reversal */}
        <CinematicReveal delay={700} animationType="sequential">
          <div className="mb-16">
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-sm text-gray-200">
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-green-400" />
                <span className="font-medium">30-Day Money-Back Guarantee</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-400" />
                <span className="font-medium">Implementation Support Included</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4 text-yellow-400" />
                <span className="font-medium">Exclusive Access Only</span>
              </div>
            </div>
          </div>
        </CinematicReveal>

        {/* Benefit-Focused Cards with Staggered Animation */}
        <StaggeredReveal
          delay={500}
          staggerDelay={120}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="group bg-slate-800 p-8 rounded-3xl border border-white/20 hover:border-white/30 transition-all duration-500 hover-lift hover-glow"
            >
              <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${benefit.gradient} flex items-center justify-center group-hover:scale-105 transition-transform duration-300 shadow-glow-subtle`}>
                <benefit.icon className={`w-8 h-8 ${benefit.color} group-hover:animate-pulse-subtle`} />
              </div>
              <div className="text-center mb-2">
                <span className="inline-block bg-green-500/10 text-green-400 text-xs font-bold px-3 py-1 rounded-full border border-green-500/20">
                  {benefit.proof}
                </span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4 group-hover:text-green-400 transition-colors duration-300 text-balance">
                {benefit.title}
              </h3>
              <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300 text-balance">
                {benefit.description}
              </p>
            </div>
          ))}
        </StaggeredReveal>

        {/* Social Proof Stats with Scroll Animation */}
        <ScrollReveal delay={700} direction="up" distance={60}>
          <div className="mt-24">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-2">Join the Elite Who Are Already Winning</h3>
              <p className="text-gray-200">Real results from real clients in the last 12 months</p>
            </div>
            <StaggeredReveal
              delay={800}
              staggerDelay={80}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto"
            >
              {[
                { value: "$1M+", label: "Under Management", color: "text-green-400" },
                { value: "45%", label: "Average Client ROI", color: "text-blue-400" },
                { value: "50+", label: "Growing Businesses", color: "text-purple-400" },
                { value: "48hrs", label: "Average Setup Time", color: "text-yellow-400" }
              ].map((stat, index) => (
                <div
                  key={index}
                  className="text-center group hover-lift"
                >
                  <div className={`text-3xl md:text-4xl font-black mb-3 group-hover:scale-105 transition-transform duration-300 letter-spacing-tight ${stat.color}`}>
                    {stat.value}
                  </div>
                  <div className="text-xs text-gray-200 font-semibold uppercase tracking-wider letter-spacing-wide">
                    {stat.label}
                  </div>
                </div>
              ))}
            </StaggeredReveal>
          </div>
        </ScrollReveal>
      </div>

      <ScrollIndicator />
    </section>
  );
};
