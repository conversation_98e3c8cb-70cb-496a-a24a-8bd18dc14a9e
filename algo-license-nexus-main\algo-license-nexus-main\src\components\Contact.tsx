import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LoadingButton, ProgressBar } from "@/components/ui/loading";
import { useSuccessNotification, useErrorNotification } from "@/components/ui/notification";
import { Mail, Phone, MapPin, Clock, CheckCircle, AlertCircle } from "lucide-react";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  interest: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    interest: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<{[key: string]: boolean}>({});
  const [submitProgress, setSubmitProgress] = useState(0);

  const formRef = useRef<HTMLFormElement>(null);
  const showSuccess = useSuccessNotification();
  const showError = useErrorNotification();

  // Enhanced form validation
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'firstName':
      case 'lastName':
        return value.trim().length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? 'Please enter a valid email address' : '';
      case 'company':
        return value.trim().length < 2 ? 'Company name is required' : '';
      case 'interest':
        return !value ? 'Please select your primary interest' : '';
      case 'message':
        return value.trim().length < 10 ? 'Message must be at least 10 characters' : '';
      default:
        return '';
    }
  };

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Real-time validation
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({ ...prev, [name]: error }));
    }
  };

  const handleBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const error = validateField(name, formData[name as keyof FormData]);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      showError('Validation Error', 'Please fix the errors in the form before submitting.');
      return;
    }

    setIsSubmitting(true);
    setSubmitProgress(0);

    // Simulate progressive form submission with realistic steps
    const steps = [
      { progress: 20, message: 'Validating information...' },
      { progress: 40, message: 'Checking availability...' },
      { progress: 60, message: 'Preparing analysis...' },
      { progress: 80, message: 'Sending request...' },
      { progress: 100, message: 'Complete!' }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 300));
      setSubmitProgress(step.progress);
    }

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      setSubmitProgress(0);

      showSuccess(
        'Request Submitted Successfully!',
        'Our team will contact you within 2 hours with your personalized ROI analysis.',
        {
          duration: 8000,
          action: {
            label: 'View Pricing',
            onClick: () => window.location.href = '/pricing'
          }
        }
      );

      // Reset form after successful submission
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          interest: '',
          message: ''
        });
        setTouched({});
        setErrors({});
        formRef.current?.reset();
      }, 2000);
    }, 500);
  };

  return (
    <section className="py-24 px-6 bg-gradient-to-r from-slate-900/50 to-blue-900/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-green-500/10 border border-green-500/20 px-4 py-2 rounded-full mb-6">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 font-semibold text-sm">FREE ROI ANALYSIS AVAILABLE</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Claim Your <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Competitive Edge</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-4">
            Get a free analysis showing exactly how much additional profit you could generate with our AI strategies.
          </p>
          <div className="glass-strong border border-red-500/30 rounded-xl p-4 max-w-2xl mx-auto bg-red-500/15">
            <p className="text-red-300 font-bold">
              ⚠️ WARNING: Only 12 exclusive licenses remaining this quarter.
              <br />Don't let your competitors secure the last spots.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="bg-slate-800 border-white/30">
            <CardHeader>
              <CardTitle className="text-2xl text-white">🚀 Get Your FREE ROI Analysis</CardTitle>
              <CardDescription className="text-gray-400">
                See exactly how much profit you're leaving on the table. Our specialists respond within 2 hours.
              </CardDescription>
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mt-4">
                <p className="text-green-400 font-semibold text-sm">
                  ✅ No obligation • ✅ Completely confidential • ✅ Custom analysis for your portfolio
                </p>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-gray-300 flex items-center">
                    First Name
                    {touched.firstName && !errors.firstName && (
                      <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                    )}
                  </Label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    onBlur={() => handleBlur('firstName')}
                    className={`bg-white/10 border-white/20 text-white placeholder:text-gray-500 transition-all duration-300 focus-ring ${
                      errors.firstName ? 'border-red-500/50 bg-red-500/5' :
                      touched.firstName && !errors.firstName ? 'border-green-500/50 bg-green-500/5' : ''
                    }`}
                  />
                  {errors.firstName && (
                    <div className="flex items-center text-red-400 text-sm mt-1">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.firstName}
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-gray-300 flex items-center">
                    Last Name
                    {touched.lastName && !errors.lastName && (
                      <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                    )}
                  </Label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    onBlur={() => handleBlur('lastName')}
                    className={`bg-white/10 border-white/20 text-white placeholder:text-gray-500 transition-all duration-300 focus-ring ${
                      errors.lastName ? 'border-red-500/50 bg-red-500/5' :
                      touched.lastName && !errors.lastName ? 'border-green-500/50 bg-green-500/5' : ''
                    }`}
                  />
                  {errors.lastName && (
                    <div className="flex items-center text-red-400 text-sm mt-1">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errors.lastName}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-300 flex items-center">
                  Email
                  {touched.email && !errors.email && (
                    <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                  )}
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  onBlur={() => handleBlur('email')}
                  className={`bg-white/10 border-white/20 text-white placeholder:text-gray-500 transition-all duration-300 focus-ring ${
                    errors.email ? 'border-red-500/50 bg-red-500/5' :
                    touched.email && !errors.email ? 'border-green-500/50 bg-green-500/5' : ''
                  }`}
                />
                {errors.email && (
                  <div className="flex items-center text-red-400 text-sm mt-1">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.email}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="company" className="text-gray-300 flex items-center">
                  Company
                  {touched.company && !errors.company && (
                    <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                  )}
                </Label>
                <Input
                  id="company"
                  placeholder="Your Investment Firm"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  onBlur={() => handleBlur('company')}
                  className={`bg-white/10 border-white/20 text-white placeholder:text-gray-500 transition-all duration-300 focus-ring ${
                    errors.company ? 'border-red-500/50 bg-red-500/5' :
                    touched.company && !errors.company ? 'border-green-500/50 bg-green-500/5' : ''
                  }`}
                />
                {errors.company && (
                  <div className="flex items-center text-red-400 text-sm mt-1">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.company}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="interest" className="text-gray-300 flex items-center">
                  Primary Interest
                  {touched.interest && !errors.interest && (
                    <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                  )}
                </Label>
                <Select
                  value={formData.interest}
                  onValueChange={(value) => handleInputChange('interest', value)}
                  onOpenChange={() => handleBlur('interest')}
                >
                  <SelectTrigger className={`bg-white/10 border-white/20 text-white transition-all duration-300 focus-ring ${
                    errors.interest ? 'border-red-500/50 bg-red-500/5' :
                    touched.interest && !errors.interest ? 'border-green-500/50 bg-green-500/5' : ''
                  }`}>
                    <SelectValue placeholder="Select strategy type" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-white/20">
                    <SelectItem value="quantum-trading">Quantum Trading Algorithms</SelectItem>
                    <SelectItem value="neural-prediction">Neural Prediction Engine</SelectItem>
                    <SelectItem value="portfolio-optimizer">Adaptive Portfolio Optimizer</SelectItem>
                    <SelectItem value="multi-asset">Multi-Asset Strategy Suite</SelectItem>
                    <SelectItem value="custom">Custom Strategy Development</SelectItem>
                  </SelectContent>
                </Select>
                {errors.interest && (
                  <div className="flex items-center text-red-400 text-sm mt-1">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.interest}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="message" className="text-gray-300 flex items-center">
                  Message
                  {touched.message && !errors.message && (
                    <CheckCircle className="w-4 h-4 text-green-400 ml-2" />
                  )}
                </Label>
                <Textarea
                  id="message"
                  placeholder="Tell us about your specific requirements and investment goals..."
                  rows={4}
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  onBlur={() => handleBlur('message')}
                  className={`bg-white/10 border-white/20 text-white placeholder:text-gray-500 transition-all duration-300 focus-ring resize-none ${
                    errors.message ? 'border-red-500/50 bg-red-500/5' :
                    touched.message && !errors.message ? 'border-green-500/50 bg-green-500/5' : ''
                  }`}
                />
                {errors.message && (
                  <div className="flex items-center text-red-400 text-sm mt-1">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.message}
                  </div>
                )}
                <div className="text-xs text-gray-400 text-right">
                  {formData.message.length}/500 characters
                </div>
              </div>

              {/* Progress bar during submission */}
              {isSubmitting && (
                <div className="space-y-2">
                  <ProgressBar
                    progress={submitProgress}
                    showLabel={true}
                    color="green"
                    className="mb-4"
                  />
                </div>
              )}

              <LoadingButton
                isLoading={isSubmitting}
                loadingText="Submitting Request..."
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 rounded-xl transition-all duration-300 hover:scale-105 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed hover-glow-pulse"
              >
                {isSubmitted ? (
                  <>
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Request Submitted Successfully!
                  </>
                ) : (
                  <>
                    🔥 Get My FREE ROI Analysis Now
                  </>
                )}
              </LoadingButton>

              <div className="text-center text-xs text-gray-300 mt-2 font-medium">
                💳 No payment required • 🔒 100% secure • ⚡ 2-hour response guarantee
              </div>
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-8">
            <Card className="bg-slate-800 border-white/30">
              <CardHeader>
                <CardTitle className="text-xl text-white">Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Email</div>
                    <div className="text-gray-200"><EMAIL></div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Phone</div>
                    <div className="text-gray-200">+****************</div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Office</div>
                    <div className="text-gray-200">Remote Operations</div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Business Hours</div>
                    <div className="text-gray-200">Mon-Fri: 9AM-6PM EST</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/80 backdrop-blur-sm border-blue-500/30 relative z-10">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Confidentiality Guaranteed</h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  All communications are protected by strict NDAs. Your strategic discussions and requirements
                  remain completely confidential throughout our engagement process.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};
